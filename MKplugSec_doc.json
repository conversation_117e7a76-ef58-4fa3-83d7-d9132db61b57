{"openapi": "3.1.0", "info": {"title": "MK Plugin Security Bot API", "description": "API для получения отчетов и статистики. Автор: @mkultra6969.", "version": "0.0.5"}, "servers": [{"url": "/plugsec"}], "paths": {"/api/v1/auth/telegram": {"post": {"tags": ["Authentication"], "summary": "Авторизация через Telegram", "description": "Обрабатывает данные от виджета \"Login with Telegram\".\nПроверяет их подлинность и в случае успеха возвращает JWT токен.", "operationId": "login_with_telegram_api_v1_auth_telegram_post", "requestBody": {"content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Auth Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/me": {"get": {"tags": ["User Profile"], "summary": "Read Users Me", "description": "Возвращает информацию и статистику о текущем авторизованном пользователе.", "operationId": "read_users_me_api_v1_users_me_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/users/me/history": {"get": {"tags": ["User Profile"], "summary": "Read User History", "operationId": "read_user_history_api_v1_users_me_history_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/report/{file_hash}": {"get": {"tags": ["Reports"], "summary": "Получить отчет по хэшу файла", "operationId": "get_report_by_hash_endpoint_api_v1_report__file_hash__get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "file_hash", "in": "path", "required": true, "schema": {"type": "string", "title": "File Hash"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/batch": {"post": {"tags": ["Reports"], "summary": "Получить несколько отчетов по списку хэшей", "operationId": "get_reports_by_hashes_endpoint_api_v1_reports_batch_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchHashesRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/api/v1/reports/search": {"get": {"tags": ["Reports"], "summary": "Поиск отчетов с фильтрацией (для авторизованных пользователей)", "description": "Позволяет искать отчеты по различным критериям с пагинацией.\nДоступно только авторизованным пользователям.\n\n- **query**: Часть имени плагина или начало хэша для поиска.\n- **verdict**: Ключевое слово из вердикта (напр., 'Опасно', 'Безопасно').\n- **approved**: Статус одобрения админом (`true` или `false`).\n- **limit**: Количество записей на странице (макс. 100).\n- **offset**: Смещение для пагинации.", "operationId": "search_reports_endpoint_api_v1_reports_search_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "query", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Query"}}, {"name": "verdict", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Verdict"}}, {"name": "approved", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Approved"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 25, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Offset"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/developer/{developer_id}/stats": {"get": {"tags": ["Statistics"], "summary": "Получить статистику по ID разработчика", "description": "Возвращает статистику по плагинам от конкретного доверенного разработчика.", "operationId": "get_developer_stats_endpoint_api_v1_developer__developer_id__stats_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "developer_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Developer Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/channel/{channel_id}/stats": {"get": {"tags": ["Statistics"], "summary": "Получить статистику по ID канала", "description": "Возвращает статистику по плагинам, загруженным из конкретного канала.", "operationId": "get_channel_stats_endpoint_api_v1_channel__channel_id__stats_get", "security": [{"APIKeyHeader": []}], "parameters": [{"name": "channel_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Channel Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/stats": {"get": {"tags": ["Statistics"], "summary": "Получить глобальную статистику", "operationId": "get_global_stats_endpoint_api_v1_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}, "security": [{"APIKeyHeader": []}]}}, "/api/v1/scan/upload": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Scan Upload Endpoint", "operationId": "scan_upload_endpoint_api_v1_scan_upload_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_scan_upload_endpoint_api_v1_scan_upload_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}}, "components": {"schemas": {"ApiResponse": {"properties": {"status": {"type": "string", "title": "Status", "default": "success"}, "data": {"title": "Data"}}, "type": "object", "required": ["data"], "title": "ApiResponse"}, "BatchHashesRequest": {"properties": {"hashes": {"items": {"type": "string"}, "type": "array", "title": "<PERSON>hes"}}, "type": "object", "required": ["hashes"], "title": "BatchHashesRequest"}, "Body_scan_upload_endpoint_api_v1_scan_upload_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_scan_upload_endpoint_api_v1_scan_upload_post"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "TokenResponse": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "token_type": {"type": "string", "title": "Token Type", "default": "bearer"}}, "type": "object", "required": ["access_token"], "title": "TokenResponse", "description": "Модель для ответа с JWT токеном."}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"OAuth2PasswordBearer": {"type": "oauth2", "flows": {"password": {"scopes": {}, "tokenUrl": "api/v1/auth/telegram"}}}, "APIKeyHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-Key"}}}}