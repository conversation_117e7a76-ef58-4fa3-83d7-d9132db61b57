<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MK Plugin Security - Безопасность плагинов</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-shield-alt"></i>
                <span>MK Plugin Security</span>
            </div>
            <div class="nav-menu">
                <a href="#home" class="nav-link active">Главная</a>
                <a href="#search" class="nav-link">Поиск</a>
                <a href="#stats" class="nav-link">Статистика</a>
                <a href="#scan" class="nav-link">Сканирование</a>
            </div>
            <div class="nav-auth">
                <button id="loginBtn" class="btn btn-primary">
                    <i class="fab fa-telegram"></i>
                    Войти через Telegram
                </button>
                <div id="userProfile" class="user-profile hidden">
                    <img id="userAvatar" src="" alt="Avatar" class="avatar">
                    <span id="userName"></span>
                    <button id="logoutBtn" class="btn btn-secondary">Выйти</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title">
                    Безопасность плагинов
                    <span class="gradient-text">нового уровня</span>
                </h1>
                <p class="hero-description">
                    Проверяйте безопасность плагинов с помощью передовых технологий анализа. 
                    Получайте детальные отчеты и статистику в режиме реального времени.
                </p>
                <div class="hero-actions">
                    <button class="btn btn-primary btn-large" onclick="scrollToSection('search')">
                        <i class="fas fa-search"></i>
                        Начать проверку
                    </button>
                    <button class="btn btn-outline btn-large" onclick="scrollToSection('stats')">
                        <i class="fas fa-chart-bar"></i>
                        Посмотреть статистику
                    </button>
                </div>
            </div>
            <div class="hero-visual">
                <div class="security-animation">
                    <div class="shield-container">
                        <i class="fas fa-shield-alt shield-icon"></i>
                        <div class="pulse-ring"></div>
                        <div class="pulse-ring delay-1"></div>
                        <div class="pulse-ring delay-2"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Section -->
    <section id="search" class="section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Поиск отчетов</h2>
                <p class="section-description">Найдите информацию о безопасности плагинов по хэшу или названию</p>
            </div>
            
            <div class="search-container">
                <div class="search-tabs">
                    <button class="tab-btn active" data-tab="hash">По хэшу</button>
                    <button class="tab-btn" data-tab="batch">Пакетный поиск</button>
                    <button class="tab-btn" data-tab="advanced">Расширенный поиск</button>
                </div>

                <div class="tab-content">
                    <!-- Hash Search -->
                    <div id="hash-tab" class="tab-pane active">
                        <div class="search-form">
                            <div class="input-group">
                                <input type="text" id="hashInput" placeholder="Введите хэш файла..." class="form-input">
                                <button id="searchHashBtn" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                    Найти
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Batch Search -->
                    <div id="batch-tab" class="tab-pane">
                        <div class="search-form">
                            <textarea id="batchHashes" placeholder="Введите хэши файлов (по одному на строку)..." class="form-textarea"></textarea>
                            <button id="searchBatchBtn" class="btn btn-primary">
                                <i class="fas fa-list"></i>
                                Найти все
                            </button>
                        </div>
                    </div>

                    <!-- Advanced Search -->
                    <div id="advanced-tab" class="tab-pane">
                        <div class="search-form advanced-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>Поисковый запрос</label>
                                    <input type="text" id="queryInput" placeholder="Название плагина или начало хэша" class="form-input">
                                </div>
                                <div class="form-group">
                                    <label>Вердикт</label>
                                    <select id="verdictSelect" class="form-select">
                                        <option value="">Все</option>
                                        <option value="Безопасно">Безопасно</option>
                                        <option value="Опасно">Опасно</option>
                                        <option value="Подозрительно">Подозрительно</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>Статус одобрения</label>
                                    <select id="approvedSelect" class="form-select">
                                        <option value="">Все</option>
                                        <option value="true">Одобрено</option>
                                        <option value="false">Не одобрено</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Лимит результатов</label>
                                    <input type="number" id="limitInput" value="25" min="1" max="100" class="form-input">
                                </div>
                            </div>
                            <button id="searchAdvancedBtn" class="btn btn-primary">
                                <i class="fas fa-filter"></i>
                                Расширенный поиск
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Results -->
            <div id="searchResults" class="search-results hidden">
                <div class="results-header">
                    <h3>Результаты поиска</h3>
                    <div class="results-count">
                        <span id="resultsCount">0</span> результатов найдено
                    </div>
                </div>
                <div id="resultsList" class="results-list"></div>
                <div id="pagination" class="pagination hidden"></div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section id="stats" class="section bg-light">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Статистика</h2>
                <p class="section-description">Общая статистика системы безопасности плагинов</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-shield-check"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalScanned">-</div>
                        <div class="stat-label">Всего проверено</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon safe">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="safePlugins">-</div>
                        <div class="stat-label">Безопасных плагинов</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="dangerousPlugins">-</div>
                        <div class="stat-label">Опасных плагинов</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalDevelopers">-</div>
                        <div class="stat-label">Разработчиков</div>
                    </div>
                </div>
            </div>

            <div class="stats-details">
                <div class="stats-section">
                    <h3>Статистика по разработчикам</h3>
                    <div class="input-group">
                        <input type="number" id="developerIdInput" placeholder="ID разработчика" class="form-input">
                        <button id="getDeveloperStatsBtn" class="btn btn-primary">
                            <i class="fas fa-user"></i>
                            Получить статистику
                        </button>
                    </div>
                    <div id="developerStats" class="stats-result hidden"></div>
                </div>

                <div class="stats-section">
                    <h3>Статистика по каналам</h3>
                    <div class="input-group">
                        <input type="number" id="channelIdInput" placeholder="ID канала" class="form-input">
                        <button id="getChannelStatsBtn" class="btn btn-primary">
                            <i class="fas fa-hashtag"></i>
                            Получить статистику
                        </button>
                    </div>
                    <div id="channelStats" class="stats-result hidden"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Scan Section -->
    <section id="scan" class="section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Сканирование файлов</h2>
                <p class="section-description">Загрузите файл для проверки на безопасность</p>
            </div>

            <div class="scan-container">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div class="upload-text">
                        <h3>Перетащите файл сюда или нажмите для выбора</h3>
                        <p>Поддерживаются файлы плагинов различных форматов</p>
                    </div>
                    <input type="file" id="fileInput" class="file-input" accept=".jar,.py,.js,.zip">
                    <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                        <i class="fas fa-folder-open"></i>
                        Выбрать файл
                    </button>
                </div>

                <div id="scanProgress" class="scan-progress hidden">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-text">Сканирование файла...</div>
                </div>

                <div id="scanResult" class="scan-result hidden"></div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <i class="fas fa-shield-alt"></i>
                    <span>MK Plugin Security</span>
                </div>
                <div class="footer-text">
                    <p>Система безопасности плагинов. Автор: @mkultra6969</p>
                    <p>API версия 0.0.5</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay hidden">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">Загрузка...</div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <script src="config.js"></script>
    <script src="script.js"></script>
</body>
</html>
